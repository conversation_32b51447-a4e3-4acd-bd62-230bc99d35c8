'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount } from 'wagmi';
import {
  WalletIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  InfoIcon,
  GlobeIcon,
  LinkIcon,
  PlusIcon,
  SearchIcon,
  UserPlusIcon
} from "lucide-react";

import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper";
import { ENSRootRegistration } from "@/components/ens/ens-root-registration";
import { SubnameAvailabilityChecker } from "@/components/ens/subname-availability-checker";
import { SubnameClaiming } from "@/components/ens/subname-claiming";
import { ENSConnection, TransactionDetails } from "@/lib/types/ens";

interface Application {
  id: string;
  name: string;
  description: string;
}

export default function ENSIntegrationPage() {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [applications, setApplications] = useState<Application[]>([]);
  const [isLoadingApplications, setIsLoadingApplications] = useState(false);
  const [ensConnections, setEnsConnections] = useState<ENSConnection[]>([]);
  const [selectedApplicationId, setSelectedApplicationId] = useState('');
  const [activeTab, setActiveTab] = useState<'register' | 'check' | 'claim'>('register');

  // Handle successful ENS root registration
  const handleENSRootSuccess = (data: any) => {
    showToast({
      type: 'success',
      title: 'ENS Root Registered',
      description: `Successfully registered ${data.name} for your application`
    });
    
    // Add to connections
    const newConnection: ENSConnection = {
      id: Date.now().toString(),
      projectId: selectedApplicationId,
      ensName: data.name,
      owner: address || '',
      connectedAt: new Date().toISOString(),
      isActive: data.isActive,
      contractAddress: data.contractAddress,
      chain: 'sepolia'
    };
    
    setEnsConnections(prev => [...prev, newConnection]);
  };

  // Handle successful subname claim
  const handleSubnameClaimSuccess = (data: TransactionDetails) => {
    showToast({
      type: 'success',
      title: 'Subname Claimed',
      description: `Subname claimed successfully! Transaction: ${data.hash.slice(0, 10)}...`
    });
  };

  // Fetch applications from API
  const fetchApplications = useCallback(async () => {
    if (!token) return;

    setIsLoadingApplications(true);
    try {
      const response = await apiService.getApplications(token);
      if (response.success) {
        const apps = (response.data || []).map((app: any) => ({
          id: app.id,
          name: app.name,
          description: app.description || 'No description available'
        }));
        setApplications(apps);
        
        // Auto-select first application if available
        if (apps.length > 0 && !selectedApplicationId) {
          setSelectedApplicationId(apps[0].id);
        }
      } else {
        console.error('Failed to fetch applications:', response.error);
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error);
    } finally {
      setIsLoadingApplications(false);
    }
  }, [token, selectedApplicationId]);

  // Load applications on component mount
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  // Load existing ENS connections (mock data - replace with API call)
  useEffect(() => {
    const mockConnections: ENSConnection[] = [
      {
        id: '1',
        projectId: '1',
        ensName: 'defiprotocol.eth',
        owner: '******************************************',
        connectedAt: '2024-01-15T10:30:00Z',
        isActive: true,
        chain: 'sepolia'
      }
    ];
    setEnsConnections(mockConnections);
  }, []);

  const getApplicationName = (appId: string) => {
    return applications.find(app => app.id === appId)?.name || 'Unknown Application';
  };

  return (
    <DashboardLayoutWrapper title="ENS Integration">
      <div className="flex gap-6 lg:gap-8">
        {/* Fixed Instructions Sidebar */}
        <aside className="w-80 bg-white/90 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-lg p-6 overflow-y-auto max-h-[calc(100vh-200px)] sticky top-0">
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-4 flex items-center gap-2">
                <InfoIcon className="h-5 w-5 text-[#4A148C]" />
                ENS Guide
              </h2>
            </div>

            {/* What is ENS */}
            <Card className="bg-gradient-to-br from-[#4A148C]/5 to-[#7B1FA2]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                  <InfoIcon className="h-4 w-4" />
                  What is ENS?
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-[#4A148C]/80 mb-3 leading-relaxed">
                  The Ethereum Name Service (ENS) is a distributed naming system that maps
                  human-readable names like &apos;alice.eth&apos; to wallet addresses and other resources.
                </p>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Decentralized
                  </Badge>
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Secure
                  </Badge>
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Permanent
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* How to Get ENS */}
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-[#4A148C] font-semibold">
                  <GlobeIcon className="h-4 w-4 text-green-600" />
                  How to Get ENS
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <div className="space-y-2">
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">1</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Visit app.ens.domains</p>
                      <p className="text-gray-600">Search for available names</p>
                    </div>
                  </div>
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">2</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Register & Pay</p>
                      <p className="text-gray-600">Complete registration with ETH</p>
                    </div>
                  </div>
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">3</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Set Primary Name</p>
                      <p className="text-gray-600">Configure in your wallet</p>
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-xs border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
                  onClick={() => window.open('https://app.ens.domains', '_blank')}
                >
                  <ExternalLinkIcon className="mr-1 h-3 w-3" />
                  Get Your ENS Name
                </Button>
              </CardContent>
            </Card>

            {/* Integration Steps */}
            <Card className="bg-gradient-to-br from-[#7B1FA2]/10 to-[#4A148C]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                  <LinkIcon className="h-4 w-4" />
                  Integration Steps
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">1</div>
                    <span className="text-[#4A148C] font-medium ml-2">Register ENS Root</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">2</div>
                    <span className="text-[#4A148C] font-medium ml-2">Check Availability</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">3</div>
                    <span className="text-[#4A148C] font-medium ml-2">Claim Subnames</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-[#4A148C] font-semibold">
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  Benefits
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-1 text-xs">
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Human-readable addresses</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Web3 identity & reputation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Cross-platform compatibility</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Decentralized ownership</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </aside>

        {/* Main Scrollable Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="flex gap-2 p-1 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 rounded-xl">
              <Button
                variant={activeTab === 'register' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('register')}
                className={`flex-1 ${activeTab === 'register' ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'text-[#4A148C]'}`}
              >
                <PlusIcon className="mr-2 h-4 w-4" />
                Register Root
              </Button>
              <Button
                variant={activeTab === 'check' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('check')}
                className={`flex-1 ${activeTab === 'check' ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'text-[#4A148C]'}`}
              >
                <SearchIcon className="mr-2 h-4 w-4" />
                Check Availability
              </Button>
              <Button
                variant={activeTab === 'claim' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('claim')}
                className={`flex-1 ${activeTab === 'claim' ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'text-[#4A148C]'}`}
              >
                <UserPlusIcon className="mr-2 h-4 w-4" />
                Claim Subname
              </Button>
            </div>

            {/* Wallet Connection Status */}
            {!isConnected && (
              <Card className="p-6 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-2xl">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-yellow-800 mb-2">Connect Your Wallet</h3>
                    <p className="text-sm text-yellow-700">
                      Connect your wallet to start using ENS functionality.
                    </p>
                  </div>
                  <ConnectButton />
                </div>
              </Card>
            )}

            {/* Application Selection */}
            {isConnected && (
              <Card className="p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
                <h3 className="font-semibold text-[#4A148C] mb-4">Select Application</h3>
                {isLoadingApplications ? (
                  <div className="text-center py-4">
                    <div className="text-sm text-gray-600">Loading applications...</div>
                  </div>
                ) : applications.length > 0 ? (
                  <div className="grid gap-3">
                    {applications.map(app => (
                      <Button
                        key={app.id}
                        variant={selectedApplicationId === app.id ? 'default' : 'outline'}
                        onClick={() => setSelectedApplicationId(app.id)}
                        className={`justify-start p-4 h-auto ${
                          selectedApplicationId === app.id
                            ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                            : 'border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10'
                        }`}
                      >
                        <div className="text-left">
                          <div className="font-medium">{app.name}</div>
                          <div className="text-xs opacity-70">{app.description}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <div className="text-sm text-gray-600 mb-2">No applications found</div>
                    <Button
                      onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                      size="sm"
                      className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                    >
                      Create Application
                    </Button>
                  </div>
                )}
              </Card>
            )}

            {/* Tab Content */}
            {activeTab === 'register' && selectedApplicationId && (
              <ENSRootRegistration
                applicationId={selectedApplicationId}
                onSuccess={handleENSRootSuccess}
                onError={(error) => console.error('ENS Root Registration Error:', error)}
              />
            )}

            {activeTab === 'check' && (
              <SubnameAvailabilityChecker
                ensRoot={ensConnections.find(conn => conn.projectId === selectedApplicationId)?.ensName}
              />
            )}

            {activeTab === 'claim' && (
              <SubnameClaiming
                ensRoot={ensConnections.find(conn => conn.projectId === selectedApplicationId)?.ensName}
                onSuccess={handleSubnameClaimSuccess}
                onError={(error) => console.error('Subname Claim Error:', error)}
                showAccountAbstraction={true}
              />
            )}

            {/* No Application Selected */}
            {isConnected && applications.length === 0 && (
              <Card className="p-8 text-center bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
                <div className="text-gray-500">
                  <WalletIcon className="h-12 w-12 mx-auto mb-4 opacity-50 text-[#4A148C]" />
                  <p className="text-lg font-medium mb-2 text-[#4A148C]">No Applications Found</p>
                  <p className="text-sm mb-4">Create an application first to use ENS functionality</p>
                  <Button
                    onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    Create Application
                  </Button>
                </div>
              </Card>
            )}
          </div>

          {/* Connected ENS Names */}
          <div className="mt-8">
            <h2 className="text-2xl font-semibold mb-6 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">Connected ENS Names</h2>

            {ensConnections.length === 0 ? (
              <Card className="p-8 text-center bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                <div className="text-gray-500">
                  <WalletIcon className="h-12 w-12 mx-auto mb-4 opacity-50 text-[#4A148C]" />
                  <p className="text-lg font-medium mb-2 text-[#4A148C]">No ENS names connected</p>
                  <p className="text-sm">Connect your first ENS name to get started</p>
                </div>
              </Card>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {ensConnections.map((connection, index) => (
                  <Card key={index} className="p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-lg text-[#4A148C]">
                          {connection.ensName}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {getApplicationName(connection.projectId)}
                        </p>
                      </div>
                      {connection.avatar && (
                        <div className="w-10 h-10 bg-gradient-to-br from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center text-white font-semibold">
                          NFT
                        </div>
                      )}
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Owner:</span>
                        <span className="font-mono">{connection.owner.slice(0, 6)}...{connection.owner.slice(-4)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Connected:</span>
                        <span>{new Date(connection.connectedAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Chain:</span>
                        <Badge variant="secondary" className="text-xs">
                          {connection.chain}
                        </Badge>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <Badge className={connection.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                        <CheckCircleIcon className="mr-1 h-3 w-3" />
                        {connection.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </DashboardLayoutWrapper>
  );
}
