import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { apiService } from '@/lib/api';

// Validation schema for reset password
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  newPassword: z.string().min(6, 'Password must be at least 6 characters long'),
});

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        error: 'Invalid JSON in request body',
      }, { status: 400 });
    }
    
    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Request body is required',
      }, { status: 400 });
    }
    
    // Validate request body
    try {
      const validatedData = resetPasswordSchema.parse(body);
      
      // Use the API service to reset password
      const result = await apiService.resetPassword({
        token: validatedData.token,
        newPassword: validatedData.newPassword
      });
      
      if (result.success) {
        return NextResponse.json(result.data, { status: 200 });
      } else {
        // Determine appropriate status code based on error
        let statusCode = 500;
        
        if (result.error?.includes('Invalid') || 
            result.error?.includes('expired') ||
            result.error?.includes('token')) {
          statusCode = 401;
        } else if (result.error?.includes('required') ||
                  result.error?.includes('Password must be')) {
          statusCode = 400;
        }
        
        return NextResponse.json({
          error: result.error,
        }, { status: statusCode });
      }
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        const errorMessage = validationError.errors.map(err => `${err.path}: ${err.message}`).join(', ');
        return NextResponse.json({
          error: `Validation error: ${errorMessage}`,
        }, { status: 422 });
      }
      
      throw validationError;
    }
  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json({
      error: 'Internal server error',
    }, { status: 500 });
  }
}
