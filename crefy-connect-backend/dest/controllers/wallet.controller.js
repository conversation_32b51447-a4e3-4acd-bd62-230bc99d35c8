"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletController = void 0;
const wallet_service_1 = require("../services/wallet.service");
class WalletController {
    static async getUserWallets(req, res) {
        try {
            const { crefyId } = req.params;
            if (!crefyId) {
                return res.status(400).json({ error: 'User ID is required' });
            }
            const wallets = await wallet_service_1.WalletService.getWalletsByUser(crefyId);
            res.status(200).json(wallets);
        }
        catch (error) {
            console.error('Error getting wallets:', error);
            res.status(500).json({ error: error instanceof Error ? error.message : 'Failed to get wallets' });
        }
    }
    static async getWallet(req, res) {
        try {
            const { crefyId, walletId } = req.params;
            if (!crefyId || !walletId) {
                return res.status(400).json({ error: 'User ID and Wallet ID are required' });
            }
            const wallet = await wallet_service_1.WalletService.getWalletById(crefyId, walletId);
            if (!wallet) {
                return res.status(404).json({ error: 'Wallet not found' });
            }
            res.status(200).json(wallet);
        }
        catch (error) {
            console.error('Error getting wallet:', error);
            res.status(500).json({ error: error instanceof Error ? error.message : 'Failed to get wallet' });
        }
    }
}
exports.WalletController = WalletController;
