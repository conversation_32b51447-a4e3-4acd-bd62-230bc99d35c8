import { Schema, model, Document, Types } from 'mongoose';

export interface IApp extends Document {
    name: string;
    appId: string;
    developer: Types.ObjectId;  // This should reference a User
    apiKey: string;
    allowedDomains: string[];
    createdAt: Date;
    updatedAt: Date;
}

const appSchema = new Schema<IApp>({
    name: {
        type: String,
        required: true,
        trim: true
    },
    appId: {
        type: String,
        required: true,
        unique: true
    },
    developer: {
        type: Schema.Types.ObjectId,
        ref: 'User',  // Changed from 'Developer' to 'User'
        required: true
    },
    apiKey: {
        type: String,
        required: true,
        unique: true
    },
    allowedDomains: [{
        type: String,
        trim: true
    }]
}, {
    timestamps: true
});

export default model<IApp>('App', appSchema);