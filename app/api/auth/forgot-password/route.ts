import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { apiService } from '@/lib/api';

// Validation schema for forgot password
const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        error: 'Invalid JSON in request body',
      }, { status: 400 });
    }
    
    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Request body is required',
      }, { status: 400 });
    }
    
    // Validate request body
    try {
      const validatedData = forgotPasswordSchema.parse(body);
      
      // Use the API service to send password reset email
      const result = await apiService.forgotPassword({
        email: validatedData.email
      });
      
      if (result.success) {
        return NextResponse.json(result.data, { status: 200 });
      } else {
        // Determine appropriate status code based on error
        let statusCode = 500;
        
        if (result.error?.includes('not found') || 
            result.error?.includes('Developer not found')) {
          statusCode = 404;
        } else if (result.error?.includes('Invalid email') || 
                  result.error?.includes('Email required')) {
          statusCode = 400;
        }
        
        return NextResponse.json({
          error: result.error,
        }, { status: statusCode });
      }
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        const errorMessage = validationError.errors.map(err => `${err.path}: ${err.message}`).join(', ');
        return NextResponse.json({
          error: `Validation error: ${errorMessage}`,
        }, { status: 422 });
      }
      
      throw validationError;
    }
  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json({
      error: 'Internal server error',
    }, { status: 500 });
  }
}
