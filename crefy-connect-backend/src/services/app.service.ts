import App from '../models/App';
import { generateApiKey, generateAppId } from '../utils/generators';
import mongoose from 'mongoose';

export class AppService {
    static async createApp(data: {
        name: string;
        developerId: string;
        allowedDomains: string[];
    }) {
        // Convert developerId to ObjectId
        const developerId = new mongoose.Types.ObjectId(data.developerId);

        const app = await App.create({
            name: data.name,
            developer: developerId,  // Use the ObjectId
            allowedDomains: data.allowedDomains || [],
            appId: generateAppId(),
            apiKey: generateApiKey()
        });

        return app;
    }

    static async getAppsByDeveloper(developerId: string) {
        const id = new mongoose.Types.ObjectId(developerId);
        return await App.find({ developer: id })
            .select('-apiKey -__v')
            .lean();
    }

    static async verifyAppCredentials(appId: string, apiKey: string) {
        return await App.findOne({ appId, apiKey })
            .select('-__v')
            .lean();
    }
}