// middleware/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import mongoose from 'mongoose';

dotenv.config();

interface JwtPayload {
    userId: string;
}

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
        return res.status(401).json({ error: 'Authorization header required' });
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Bearer token required' });
    }

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as JwtPayload;

        // Validate the userId is a proper ObjectId
        if (!mongoose.Types.ObjectId.isValid(decoded.userId)) {
            return res.status(400).json({ error: 'Invalid user ID format' });
        }

        req.user = { userId: decoded.userId };
        next();
    } catch (error) {
        const message = error instanceof Error ? error.message : 'Invalid token';
        res.status(403).json({ error: message });
    }
};

export const requireAuth = async (req: Request, res: Response, next: NextFunction) => {
    try {
        // const { crefyId } = req.body;

        // if (!crefyId) {
        //     return res.status(400).json({ message: 'crefyId is required in request body' });
        // }

        // const user = await User.findOne({ crefyId });
        // if (!user) {
        //     return res.status(404).json({ message: 'CrefyId is not valid' });
        // }

        // // Attach user and crefyId to request object
        // req.user = user;
        // // @ts-ignore
        // req.crefyId = crefyId;
        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};