import { L1SUBNAME_REGISTRAR_CONTRACT_ADDRESS, L1SUBNAME_REGISTRAR_CONTRACT_ABI, } from './ContractABIs/L1SubnameRegistrar';
import { account,publicClient, walletClient } from './config'


async function claimSubname(label : string) {
    try {
const {request} = await publicClient.simulateContract({
    account,
    address: L1SUBNAME_REGISTRAR_CONTRACT_ADDRESS,
    abi: L1SUBNAME_REGISTRAR_CONTRACT_ABI,
    functionName: 'claimName',
    args: [label]
});

const txHash = await walletClient.writeContract(request);

console.log("Transaction hash is :", txHash);
return txHash;
} catch (error) {
    console.error("Error claiming subname:", error);
    throw error;
  }
}
export default claimSubname;