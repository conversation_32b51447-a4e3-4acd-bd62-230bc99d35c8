"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOnRampTicketDto = exports.CreateOffRampTicketDto = exports.OnRampStatusDto = exports.OffRampStatusDto = exports.DepositDto = exports.OffRampDto = exports.OnRampDto = exports.GetQuoteDto = void 0;
class GetQuoteDto {
}
exports.GetQuoteDto = GetQuoteDto;
class OnRampDto {
}
exports.OnRampDto = OnRampDto;
class OffRampDto {
}
exports.OffRampDto = OffRampDto;
class DepositDto {
}
exports.DepositDto = DepositDto;
class OffRampStatusDto {
}
exports.OffRampStatusDto = OffRampStatusDto;
class OnRampStatusDto {
}
exports.OnRampStatusDto = OnRampStatusDto;
class CreateOffRampTicketDto {
}
exports.CreateOffRampTicketDto = CreateOffRampTicketDto;
class CreateOnRampTicketDto {
}
exports.CreateOnRampTicketDto = CreateOnRampTicketDto;
