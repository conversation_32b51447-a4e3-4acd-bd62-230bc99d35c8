"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const appSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    appId: {
        type: String,
        required: true,
        unique: true
    },
    developer: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User', // Changed from 'Developer' to 'User'
        required: true
    },
    apiKey: {
        type: String,
        required: true,
        unique: true
    },
    allowedDomains: [{
            type: String,
            trim: true
        }]
}, {
    timestamps: true
});
exports.default = (0, mongoose_1.model)('App', appSchema);
