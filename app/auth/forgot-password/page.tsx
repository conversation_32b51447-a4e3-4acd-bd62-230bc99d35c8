"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { apiService } from "@/lib/api";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Basic client-side validation
    if (!email.trim()) {
      setStatus("Please enter your email address.");
      return;
    }

    setIsLoading(true);
    setStatus("Sending reset link...");

    try {
      const response = await apiService.forgotPassword({ email });

      if (response.success) {
        setStatus("Password reset link sent to your email!");
        setIsSuccess(true);
      } else {
        setStatus(response.error || "Failed to send reset link");
        setIsSuccess(false);
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      setStatus("Error sending reset link. Please try again.");
      setIsSuccess(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="min-h-screen flex items-center justify-center bg-white relative overflow-hidden px-4">
      {/* Animated Background Elements */}
      <div className="absolute w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float" style={{ top: "20%", left: "10%" }}></div>
      <div className="absolute w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow" style={{ bottom: "20%", right: "10%" }}></div>

      {/* Forgot Password Card */}
      <div className="relative z-10 w-full max-w-md p-8 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl space-y-6 transition-all duration-300 hover:shadow-2xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            Reset Your Password
          </h1>
          <p className="text-gray-600 mt-2">
            {isSuccess 
              ? "Check your email for reset instructions" 
              : "Enter your email to receive a password reset link"
            }
          </p>
        </div>

        {!isSuccess ? (
          <form onSubmit={handleSubmit} className="space-y-5">
            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="<EMAIL>"
              />
            </div>

            {/* Status Message */}
            {status && (
              <p
                className={`text-sm ${
                  status.includes("Error") ||
                  status.includes("Failed") ||
                  status.includes("Please enter")
                    ? "text-red-500"
                    : status.includes("sent") || status.includes("link sent")
                    ? "text-green-600"
                    : "text-gray-600"
                }`}
              >
                {status}
              </p>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 bg-[#4A148C] text-white rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg font-medium disabled:opacity-50"
            >
              {isLoading ? "Sending..." : "Send Reset Link"}
            </button>
          </form>
        ) : (
          <div className="space-y-5">
            <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
              <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-green-800 mb-2">Email Sent!</h3>
              <p className="text-sm text-green-700">
                We've sent a password reset link to <strong>{email}</strong>. 
                Please check your email and follow the instructions to reset your password.
              </p>
            </div>

            <button
              onClick={() => {
                setIsSuccess(false);
                setEmail("");
                setStatus("");
              }}
              className="w-full py-2 text-[#4A148C] border border-[#4A148C] rounded-lg hover:bg-[#4A148C] hover:text-white transition-all duration-300 font-medium"
            >
              Send Another Reset Link
            </button>
          </div>
        )}

        <div className="text-center space-y-2">
          <p className="text-sm text-gray-600">
            Remember your password?{" "}
            <Link href="/auth/signin" className="text-[#4A148C] hover:underline font-medium">
              Sign in
            </Link>
          </p>
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?{" "}
            <Link href="/auth/signup" className="text-[#4A148C] hover:underline font-medium">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}
