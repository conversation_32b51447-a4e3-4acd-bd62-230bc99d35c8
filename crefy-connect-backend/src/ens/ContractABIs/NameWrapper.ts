export const NAMEWRAPPER_CONTRACT_ADDRESS = "0x0635513f179D50A207757E05759CbD106d7dFcE8";

export const NAMEWRAPPER_CONTRACT_ABI = [
    {
        "inputs": [
          {
            "internalType": "address",
            "name": "from",
            "type": "address"
          },
          {
            "internalType": "address",
            "name": "to",
            "type": "address"
          },
          {
            "internalType": "uint256",
            "name": "id",
            "type": "uint256"
          },
          {
            "internalType": "uint256",
            "name": "amount",
            "type": "uint256"
          },
          {
            "internalType": "bytes",
            "name": "data",
            "type": "bytes"
          }
        ],
        "name": "safeTransferFrom",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      }
    ] as const;


export type ContractAddress = typeof NAMEWRAPPER_CONTRACT_ADDRESS;
export type ContractABI = typeof NAMEWRAPPER_CONTRACT_ABI;