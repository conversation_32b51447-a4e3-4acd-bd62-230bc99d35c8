import { NextRequest, NextResponse } from 'next/server';
import { apiService } from '@/lib/api';

export async function GET(request: NextRequest) {
  try {
    // Get token from query parameters
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    
    if (!token) {
      return NextResponse.json({
        error: 'Token is required',
      }, { status: 400 });
    }
    
    // Use the API service to verify the reset token
    const result = await apiService.verifyResetToken(token);
    
    if (result.success) {
      return NextResponse.json(result.data, { status: 200 });
    } else {
      // Determine appropriate status code based on error
      let statusCode = 500;
      
      if (result.error?.includes('Invalid') || 
          result.error?.includes('expired') ||
          result.error?.includes('Token')) {
        statusCode = 401;
      } else if (result.error?.includes('required')) {
        statusCode = 400;
      }
      
      return NextResponse.json({
        error: result.error,
      }, { status: statusCode });
    }
  } catch (error) {
    console.error('Verify reset token error:', error);
    return NextResponse.json({
      error: 'Internal server error',
    }, { status: 500 });
  }
}
