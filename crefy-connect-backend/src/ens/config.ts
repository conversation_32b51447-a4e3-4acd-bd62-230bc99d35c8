import { createWalletClient, http, publicActions, createPublicClient } from "viem";
import { privateKeyToAccount } from "viem/accounts";
import { sepolia } from 'viem/chains';
import dotenv from 'dotenv';
dotenv.config();

export const publicClient = createPublicClient({
    chain: sepolia,
    transport: http(process.env.RPC_URL),
})


export const account = privateKeyToAccount(process.env.PRIVATE_KEY as `0x{string}`);

export const walletClient = createWalletClient({
    account,
    chain: sepolia,
    transport: http()
})