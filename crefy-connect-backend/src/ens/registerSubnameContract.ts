import {NAMEWRAPPER_CONTRACT_ADDRESS, NAMEWRAPPER_CONTRACT_ABI} from './ContractABIs/NameWrapper';
import {FACTORY_CONTRACT_ADDRESS, FACTORY_CONTRACT_ABI} from './ContractABIs/FactoryContract';
import {account, publicClient, walletClient} from './config';
import { normalize, namehash } from 'viem/ens';


async function registerSubnameContract(ensName: string) {
    const normalizedName = normalize(ensName);
    const node = namehash(normalizedName);

    try {
        const {request} = await publicClient.simulateContract({
            account,
            address: FACTORY_CONTRACT_ADDRESS,
            abi: FACTORY_CONTRACT_ABI,
            functionName: 'createSubnameRegistrar',
            args: [node],
        })

        const txHash = await walletClient.writeContract(request)
        console.log("Transaction hash is :", txHash);
        return txHash;
    } catch (error){
        console.error("Error registering subname contract:", error);
        throw error ;
    }

}

async function transferOwnership(
    fromAddress: `0x${string}`,
    toAddress: `0x${string}`,
    id: bigint,
) {
    try {
        const {request} = await publicClient.simulateContract({
            account,
            address: NAMEWRAPPER_CONTRACT_ADDRESS,
            abi: NAMEWRAPPER_CONTRACT_ABI,
            functionName: 'safeTransferFrom',
            args: [fromAddress, toAddress, id, BigInt(1), '0x'],
        })

        const txHash = await walletClient.writeContract(request)
        console.log("Transaction hash is :", txHash);
        return txHash;
    }catch (error) {
        console.error("Error transferring ownership:", error);
        throw error;
    }

}