'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount } from 'wagmi';
import {
  WalletIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  InfoIcon,
  GlobeIcon,
  LinkIcon,
  PlusIcon,
  SearchIcon,
  UserPlusIcon
} from "lucide-react";

import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper";
import { ENSRootRegistration } from "@/components/ens/ens-root-registration";
import { SubnameAvailabilityChecker } from "@/components/ens/subname-availability-checker";
import { SubnameClaiming } from "@/components/ens/subname-claiming";
import { ENSConnection, TransactionDetails } from "@/lib/types/ens";
import { ErrorBoundary } from "@/components/shared/error-boundary";
import { ErrorHandler, handleApiError, useErrorHandler } from "@/lib/error-handler";

interface Application {
  id: string;
  name: string;
  description: string;
}

export default function ENSIntegrationPage() {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  const { handleError } = useErrorHandler();
  
  const [applications, setApplications] = useState<Application[]>([]);
  const [isLoadingApplications, setIsLoadingApplications] = useState(false);
  const [applicationError, setApplicationError] = useState<string | null>(null);
  const [ensConnections, setEnsConnections] = useState<ENSConnection[]>([]);
  const [selectedApplicationId, setSelectedApplicationId] = useState('');
  const [activeTab, setActiveTab] = useState<'register' | 'check' | 'claim'>('register');

  // Handle successful ENS root registration
  const handleENSRootSuccess = (data: any) => {
    showToast({
      type: 'success',
      title: 'ENS Root Registered',
      description: `Successfully registered ${data.name} for your application`
    });
    
    // Add to connections
    const newConnection: ENSConnection = {
      id: Date.now().toString(),
      projectId: selectedApplicationId,
      ensName: data.name,
      owner: address || '',
      connectedAt: new Date().toISOString(),
      isActive: data.isActive,
      contractAddress: data.contractAddress,
      chain: 'sepolia'
    };
    
    setEnsConnections(prev => [...prev, newConnection]);
  };

  // Handle successful subname claim
  const handleSubnameClaimSuccess = (data: TransactionDetails) => {
    showToast({
      type: 'success',
      title: 'Subname Claimed',
      description: `Subname claimed successfully! Transaction: ${data.hash.slice(0, 10)}...`
    });
  };

  // Fetch applications from API
  const fetchApplications = useCallback(async () => {
    if (!token) return;

    setIsLoadingApplications(true);
    setApplicationError(null);

    try {
      const response = await apiService.getApplications(token);
      if (response.success) {
        const apps = (response.data || []).map((app: any) => ({
          id: app.appId || app.id,
          name: app.name,
          description: app.description || 'No description available'
        }));
        setApplications(apps);

        // Auto-select first application if available and none is currently selected
        if (apps.length > 0 && !selectedApplicationId) {
          setSelectedApplicationId(apps[0].id);
        }

        // Clear any previous errors
        setApplicationError(null);
      } else {
        const appError = handleApiError(new Error(response.error || 'Failed to fetch applications'), {
          context: 'fetchApplications',
          token: !!token
        });

        setApplicationError(appError.userMessage);
        showToast({
          type: 'error',
          title: 'Failed to Load Applications',
          description: appError.userMessage
        });

        ErrorHandler.log(appError);
      }
    } catch (error) {
      const appError = handleApiError(error, {
        context: 'fetchApplications',
        token: !!token
      });

      setApplicationError(appError.userMessage);
      showToast({
        type: 'error',
        title: 'Network Error',
        description: appError.userMessage
      });

      ErrorHandler.log(appError);
    } finally {
      setIsLoadingApplications(false);
    }
  }, [token, selectedApplicationId, showToast]);

  // Load applications on component mount
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  // Load existing ENS connections (mock data - replace with API call)
  useEffect(() => {
    const mockConnections: ENSConnection[] = [
      {
        id: '1',
        projectId: '1',
        ensName: 'defiprotocol.eth',
        owner: '******************************************',
        connectedAt: '2024-01-15T10:30:00Z',
        isActive: true,
        chain: 'sepolia'
      }
    ];
    setEnsConnections(mockConnections);
  }, []);

  const getApplicationName = (appId: string) => {
    return applications.find(app => app.id === appId)?.name || 'Unknown Application';
  };

  return (
    <ErrorBoundary>
      <DashboardLayoutWrapper title="ENS Integration">
      <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
        {/* Responsive Instructions Sidebar */}
        <aside className="w-full lg:w-80 bg-white/90 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-lg p-4 lg:p-6 overflow-y-auto max-h-[calc(100vh-200px)] lg:sticky lg:top-0 order-2 lg:order-1">
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-4 flex items-center gap-2">
                <InfoIcon className="h-5 w-5 text-[#4A148C]" />
                ENS Guide
              </h2>
            </div>

            {/* What is ENS */}
            <Card className="bg-gradient-to-br from-[#4A148C]/5 to-[#7B1FA2]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                  <InfoIcon className="h-4 w-4" />
                  What is ENS?
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-[#4A148C]/80 mb-3 leading-relaxed">
                  The Ethereum Name Service (ENS) is a distributed naming system that maps
                  human-readable names like &apos;alice.eth&apos; to wallet addresses and other resources.
                </p>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Decentralized
                  </Badge>
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Secure
                  </Badge>
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] text-xs border border-[#B497D6]/30">
                    Permanent
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* How to Get ENS */}
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-[#4A148C] font-semibold">
                  <GlobeIcon className="h-4 w-4 text-green-600" />
                  How to Get ENS
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <div className="space-y-2">
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">1</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Visit app.ens.domains</p>
                      <p className="text-gray-600">Search for available names</p>
                    </div>
                  </div>
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">2</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Register & Pay</p>
                      <p className="text-gray-600">Complete registration with ETH</p>
                    </div>
                  </div>
                  <div className="flex gap-2 text-xs">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">3</div>
                    <div>
                      <p className="font-medium text-[#4A148C]">Set Primary Name</p>
                      <p className="text-gray-600">Configure in your wallet</p>
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-xs border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
                  onClick={() => window.open('https://app.ens.domains', '_blank')}
                >
                  <ExternalLinkIcon className="mr-1 h-3 w-3" />
                  Get Your ENS Name
                </Button>
              </CardContent>
            </Card>

            {/* Integration Steps */}
            <Card className="bg-gradient-to-br from-[#7B1FA2]/10 to-[#4A148C]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                  <LinkIcon className="h-4 w-4" />
                  Integration Steps
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">1</div>
                    <span className="text-[#4A148C] font-medium ml-2">Register ENS Root</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">2</div>
                    <span className="text-[#4A148C] font-medium ml-2">Check Availability</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white/60 backdrop-blur-sm rounded-lg border border-[#B497D6]/20">
                    <div className="w-5 h-5 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-lg">3</div>
                    <span className="text-[#4A148C] font-medium ml-2">Claim Subnames</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-[#4A148C] font-semibold">
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  Benefits
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-1 text-xs">
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Human-readable addresses</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Web3 identity & reputation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Cross-platform compatibility</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4A148C]/80">Decentralized ownership</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </aside>

        {/* Main Responsive Content */}
        <main className="flex-1 overflow-y-auto order-1 lg:order-2">
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="flex gap-2 p-1 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 rounded-xl">
              <Button
                variant={activeTab === 'register' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('register')}
                className={`flex-1 ${activeTab === 'register' ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'text-[#4A148C]'}`}
              >
                <PlusIcon className="mr-2 h-4 w-4" />
                Register Root
              </Button>
              <Button
                variant={activeTab === 'check' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('check')}
                className={`flex-1 ${activeTab === 'check' ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'text-[#4A148C]'}`}
              >
                <SearchIcon className="mr-2 h-4 w-4" />
                Check Availability
              </Button>
              <Button
                variant={activeTab === 'claim' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('claim')}
                className={`flex-1 ${activeTab === 'claim' ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white' : 'text-[#4A148C]'}`}
              >
                <UserPlusIcon className="mr-2 h-4 w-4" />
                Claim Subname
              </Button>
            </div>

            {/* Enhanced Wallet Connection Status */}
            {!isConnected && (
              <Card className="p-8 bg-gradient-to-br from-[#4A148C]/5 via-[#7B1FA2]/5 to-[#4A148C]/10 border border-[#B497D6]/30 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="text-center space-y-6">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                    <WalletIcon className="h-8 w-8 text-white" />
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                      Connect Your Wallet
                    </h3>
                    <p className="text-gray-600 max-w-md mx-auto leading-relaxed">
                      To use ENS functionality, you need to connect your wallet. This allows us to verify
                      ENS ownership and register domains for your applications.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <ConnectButton />

                    <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        <span>Secure Connection</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        <span>ENS Compatible</span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-[#B497D6]/20">
                    <p className="text-xs text-gray-500">
                      Supported wallets: MetaMask, WalletConnect, Coinbase Wallet, and more
                    </p>
                  </div>
                </div>
              </Card>
            )}

            {/* Enhanced Application Selection */}
            {isConnected && (
              <Card className="p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                    Select Application
                  </h3>
                  {!isLoadingApplications && applications.length > 0 && (
                    <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] border border-[#B497D6]/30">
                      {applications.length} app{applications.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>

                {isLoadingApplications ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center gap-3">
                        <div className="w-5 h-5 border-2 border-[#4A148C] border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-[#4A148C] font-medium">Loading applications...</span>
                      </div>
                    </div>
                    {/* Loading skeleton */}
                    {[1, 2].map((i) => (
                      <div key={i} className="p-4 border border-[#B497D6]/20 rounded-lg animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    ))}
                  </div>
                ) : applicationError ? (
                  <div className="text-center py-8 space-y-4">
                    <div className="w-12 h-12 mx-auto bg-red-100 rounded-full flex items-center justify-center">
                      <ExternalLinkIcon className="h-6 w-6 text-red-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-red-800 mb-1">Failed to Load Applications</h4>
                      <p className="text-sm text-red-600 mb-4">{applicationError}</p>
                      <Button
                        onClick={fetchApplications}
                        size="sm"
                        variant="outline"
                        className="border-red-300 hover:bg-red-50 text-red-700"
                      >
                        <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                        Try Again
                      </Button>
                    </div>
                  </div>
                ) : applications.length > 0 ? (
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600 mb-4">
                      Choose which application you want to register an ENS domain for:
                    </p>
                    <div className="grid gap-3">
                      {applications.map(app => (
                        <Button
                          key={app.id}
                          variant={selectedApplicationId === app.id ? 'default' : 'outline'}
                          onClick={() => setSelectedApplicationId(app.id)}
                          className={`justify-start p-4 h-auto transition-all duration-200 ${
                            selectedApplicationId === app.id
                              ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white shadow-lg shadow-[#4A148C]/25'
                              : 'border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:shadow-md'
                          }`}
                        >
                          <div className="flex items-center gap-3 text-left w-full">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                              selectedApplicationId === app.id
                                ? 'bg-white/20'
                                : 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2]'
                            }`}>
                              <GlobeIcon className={`h-5 w-5 ${
                                selectedApplicationId === app.id ? 'text-white' : 'text-white'
                              }`} />
                            </div>
                            <div className="flex-1">
                              <div className="font-medium">{app.name}</div>
                              <div className={`text-xs ${
                                selectedApplicationId === app.id ? 'text-white/70' : 'text-gray-500'
                              }`}>
                                {app.description}
                              </div>
                            </div>
                            {selectedApplicationId === app.id && (
                              <CheckCircleIcon className="h-5 w-5 text-white" />
                            )}
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 space-y-4">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C]/10 to-[#7B1FA2]/10 rounded-full flex items-center justify-center">
                      <PlusIcon className="h-8 w-8 text-[#4A148C]" />
                    </div>
                    <div>
                      <h4 className="font-medium text-[#4A148C] mb-2">No Applications Found</h4>
                      <p className="text-sm text-gray-600 mb-4">
                        You need to create an application before you can register ENS domains.
                      </p>
                      <Button
                        onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                        className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25"
                      >
                        <PlusIcon className="mr-2 h-4 w-4" />
                        Create Your First Application
                      </Button>
                    </div>
                  </div>
                )}
              </Card>
            )}

            {/* Tab Content */}
            {activeTab === 'register' && selectedApplicationId && (
              <ENSRootRegistration
                applicationId={selectedApplicationId}
                onSuccess={handleENSRootSuccess}
                onError={(error) => console.error('ENS Root Registration Error:', error)}
              />
            )}

            {activeTab === 'check' && (
              <SubnameAvailabilityChecker
                ensRoot={ensConnections.find(conn => conn.projectId === selectedApplicationId)?.ensName}
              />
            )}

            {activeTab === 'claim' && (
              <SubnameClaiming
                ensRoot={ensConnections.find(conn => conn.projectId === selectedApplicationId)?.ensName}
                onSuccess={handleSubnameClaimSuccess}
                onError={(error) => console.error('Subname Claim Error:', error)}
                showAccountAbstraction={true}
              />
            )}


          </div>

          {/* Enhanced Connected ENS Names Dashboard */}
          <div className="mt-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                Connected ENS Names
              </h2>
              {ensConnections.length > 0 && (
                <div className="flex items-center gap-4">
                  <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] border border-[#B497D6]/30">
                    {ensConnections.length} domain{ensConnections.length !== 1 ? 's' : ''}
                  </Badge>
                  <Button
                    onClick={() => setActiveTab('register')}
                    size="sm"
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Add Domain
                  </Button>
                </div>
              )}
            </div>

            {ensConnections.length === 0 ? (
              <Card className="p-12 text-center bg-gradient-to-br from-[#4A148C]/5 via-white to-[#7B1FA2]/5 border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                <div className="max-w-md mx-auto space-y-6">
                  <div className="w-20 h-20 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                    <GlobeIcon className="h-10 w-10 text-white" />
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-xl font-bold text-[#4A148C]">No ENS Domains Connected</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Connect your first ENS domain to start creating subnames for your users.
                      ENS domains provide human-readable addresses and enhance your application&apos;s Web3 identity.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <Button
                      onClick={() => setActiveTab('register')}
                      className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25"
                    >
                      <PlusIcon className="mr-2 h-4 w-4" />
                      Connect Your First ENS Domain
                    </Button>

                    <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        <span>Human-readable addresses</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        <span>Web3 identity</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ) : (
              <div className="space-y-6">
                {/* ENS Connections Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {ensConnections.map((connection, index) => (
                    <Card key={index} className="group p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gradient-to-br from-[#4A148C] to-[#7B1FA2] rounded-xl flex items-center justify-center shadow-lg">
                              <GlobeIcon className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h3 className="font-bold text-lg text-[#4A148C] group-hover:text-[#7B1FA2] transition-colors">
                                {connection.ensName}
                              </h3>
                              <p className="text-sm text-gray-600">
                                {getApplicationName(connection.projectId)}
                              </p>
                            </div>
                          </div>

                          <Badge className={`${
                            connection.isActive
                              ? "bg-green-100 text-green-800 border-green-200"
                              : "bg-gray-100 text-gray-800 border-gray-200"
                          } border`}>
                            <div className={`w-2 h-2 rounded-full mr-1 ${
                              connection.isActive ? 'bg-green-600' : 'bg-gray-600'
                            }`}></div>
                            {connection.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>

                        {/* Details */}
                        <div className="space-y-3 text-sm">
                          <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                            <span className="text-gray-600 font-medium">Owner:</span>
                            <span className="font-mono bg-white px-2 py-1 rounded border text-[#4A148C]">
                              {connection.owner.slice(0, 6)}...{connection.owner.slice(-4)}
                            </span>
                          </div>

                          <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                            <span className="text-gray-600 font-medium">Connected:</span>
                            <span className="text-gray-800">
                              {new Date(connection.connectedAt).toLocaleDateString()}
                            </span>
                          </div>

                          <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                            <span className="text-gray-600 font-medium">Network:</span>
                            <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] border border-[#B497D6]/30 text-xs">
                              {connection.chain.charAt(0).toUpperCase() + connection.chain.slice(1)}
                            </Badge>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="pt-4 border-t border-gray-200 flex gap-2">
                          <Button
                            onClick={() => setActiveTab('check')}
                            size="sm"
                            variant="outline"
                            className="flex-1 border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 text-xs"
                          >
                            <SearchIcon className="mr-1 h-3 w-3" />
                            Check Subnames
                          </Button>
                          <Button
                            onClick={() => setActiveTab('claim')}
                            size="sm"
                            variant="outline"
                            className="flex-1 border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 text-xs"
                          >
                            <UserPlusIcon className="mr-1 h-3 w-3" />
                            Claim Subname
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <CheckCircleIcon className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-green-700 font-medium">Active Domains</p>
                        <p className="text-xl font-bold text-green-800">
                          {ensConnections.filter(c => c.isActive).length}
                        </p>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <GlobeIcon className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-blue-700 font-medium">Total Domains</p>
                        <p className="text-xl font-bold text-blue-800">{ensConnections.length}</p>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-4 bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <LinkIcon className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm text-purple-700 font-medium">Applications</p>
                        <p className="text-xl font-bold text-purple-800">
                          {new Set(ensConnections.map(c => c.projectId)).size}
                        </p>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </DashboardLayoutWrapper>
    </ErrorBoundary>
  );
}
