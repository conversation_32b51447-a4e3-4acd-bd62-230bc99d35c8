'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { useAccount, useChainId } from 'wagmi';
import { 
  CheckCircleIcon, 
  RefreshCwIcon, 
  AlertCircleIcon,
  ExternalLinkIcon,
  GlobeIcon,
  ShieldCheckIcon
} from "lucide-react";
import { apiService } from "@/lib/api";
import { useAuth } from "@/lib/auth-context";
import { 
  ENSRootRegistrationRequest, 
  ENSRootRegistrationProps,
  SupportedChain,
  SUPPORTED_CHAINS 
} from "@/lib/types/ens";
import { getENSOwner, isValidENSName } from "@/lib/ens-utils";
import { ethers } from 'ethers';

export function ENSRootRegistration({ 
  applicationId, 
  onSuccess, 
  onError, 
  className = "" 
}: ENSRootRegistrationProps) {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [formData, setFormData] = useState({
    ensName: '',
    chain: 'sepolia' as SupportedChain,
    contractAddress: '',
  });
  
  const [isRegistering, setIsRegistering] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [ownershipVerified, setOwnershipVerified] = useState<boolean | null>(null);
  const [currentOwner, setCurrentOwner] = useState<string | null>(null);

  // Reset verification when ENS name changes
  useEffect(() => {
    setOwnershipVerified(null);
    setCurrentOwner(null);
  }, [formData.ensName]);

  // Auto-fill contract address when connected
  useEffect(() => {
    if (isConnected && address && !formData.contractAddress) {
      setFormData(prev => ({
        ...prev,
        contractAddress: address
      }));
    }
  }, [isConnected, address, formData.contractAddress]);

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'ensName' ? value.toLowerCase() : value
    }));
  };

  const verifyENSOwnership = async () => {
    if (!formData.ensName || !isValidENSName(formData.ensName)) {
      showToast({
        type: 'error',
        title: 'Invalid ENS Name',
        description: 'Please enter a valid .eth domain name'
      });
      return;
    }

    if (!address) {
      showToast({
        type: 'error',
        title: 'Wallet Not Connected',
        description: 'Please connect your wallet to verify ownership'
      });
      return;
    }

    setIsVerifying(true);
    
    try {
      const provider = new ethers.JsonRpcProvider('https://eth.llamarpc.com');
      const owner = await getENSOwner(formData.ensName, provider);
      
      if (!owner) {
        setOwnershipVerified(false);
        setCurrentOwner(null);
        showToast({
          type: 'error',
          title: 'ENS Not Found',
          description: 'This ENS name does not exist or has no owner'
        });
        return;
      }

      setCurrentOwner(owner);
      const isOwned = owner.toLowerCase() === address.toLowerCase();
      setOwnershipVerified(isOwned);

      if (isOwned) {
        showToast({
          type: 'success',
          title: 'Ownership Verified',
          description: `You own ${formData.ensName}. You can proceed with registration.`
        });
      } else {
        showToast({
          type: 'error',
          title: 'Ownership Verification Failed',
          description: `You don't own ${formData.ensName}. Current owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`
        });
      }
    } catch (error) {
      console.error('ENS verification error:', error);
      setOwnershipVerified(false);
      showToast({
        type: 'error',
        title: 'Verification Failed',
        description: 'Failed to verify ENS ownership. Please try again.'
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleRegister = async () => {
    if (!token) {
      showToast({
        type: 'error',
        title: 'Authentication Required',
        description: 'Please log in to register an ENS root'
      });
      return;
    }

    if (!ownershipVerified) {
      showToast({
        type: 'error',
        title: 'Ownership Not Verified',
        description: 'Please verify ENS ownership before registering'
      });
      return;
    }

    if (!formData.contractAddress || !ethers.isAddress(formData.contractAddress)) {
      showToast({
        type: 'error',
        title: 'Invalid Contract Address',
        description: 'Please enter a valid Ethereum address'
      });
      return;
    }

    setIsRegistering(true);

    try {
      const registrationData: ENSRootRegistrationRequest = {
        ens_name: formData.ensName,
        contractAddress: formData.contractAddress,
        chain: formData.chain,
        isActive: true
      };

      const response = await apiService.registerENSRoot(registrationData, token);

      if (response.success && response.data) {
        showToast({
          type: 'success',
          title: 'ENS Root Registered',
          description: `Successfully registered ${formData.ensName} for your application`
        });
        
        onSuccess?.(response.data.data);
        
        // Reset form
        setFormData({
          ensName: '',
          chain: 'sepolia',
          contractAddress: address || '',
        });
        setOwnershipVerified(null);
        setCurrentOwner(null);
      } else {
        throw new Error(response.error || 'Registration failed');
      }
    } catch (error: any) {
      console.error('ENS registration error:', error);
      const errorMessage = error.message || 'Failed to register ENS root';
      
      showToast({
        type: 'error',
        title: 'Registration Failed',
        description: errorMessage
      });
      
      onError?.(errorMessage);
    } finally {
      setIsRegistering(false);
    }
  };

  const isFormValid = formData.ensName && 
                     formData.contractAddress && 
                     formData.chain && 
                     ownershipVerified === true;

  return (
    <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
      <CardHeader>
        <CardTitle className="text-xl font-semibold flex items-center gap-2 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
          <GlobeIcon className="h-5 w-5 text-[#4A148C]" />
          Register ENS Root Domain
        </CardTitle>
        <p className="text-sm text-gray-600">
          Register a .eth domain that your application can use to create subnames for users.
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* ENS Name Input */}
        <div className="space-y-3">
          <Label htmlFor="ensName" className="text-sm font-medium text-[#4A148C]">
            ENS Root Domain
          </Label>
          <div className="flex gap-2">
            <Input
              id="ensName"
              type="text"
              placeholder="myplatform"
              value={formData.ensName.replace('.eth', '')}
              onChange={(e) => handleInputChange('ensName', e.target.value + '.eth')}
              className="flex-1"
            />
            <div className="flex items-center px-3 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-600">
              .eth
            </div>
            <Button
              onClick={verifyENSOwnership}
              disabled={!formData.ensName || !isConnected || isVerifying}
              variant="outline"
              size="sm"
              className="border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10"
            >
              {isVerifying ? (
                <>
                  <RefreshCwIcon className="mr-1 h-3 w-3 animate-spin" />
                  Verifying
                </>
              ) : (
                <>
                  <ShieldCheckIcon className="mr-1 h-3 w-3" />
                  Verify
                </>
              )}
            </Button>
          </div>
          
          {/* Ownership Status */}
          {ownershipVerified !== null && (
            <div className={`p-3 rounded-lg border ${
              ownershipVerified 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center gap-2">
                {ownershipVerified ? (
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircleIcon className="h-4 w-4 text-red-600" />
                )}
                <span className={`text-sm font-medium ${
                  ownershipVerified ? 'text-green-800' : 'text-red-800'
                }`}>
                  {ownershipVerified ? 'Ownership Verified' : 'Ownership Verification Failed'}
                </span>
              </div>
              {currentOwner && (
                <p className={`text-xs mt-1 ${
                  ownershipVerified ? 'text-green-700' : 'text-red-700'
                }`}>
                  Current owner: {currentOwner.slice(0, 6)}...{currentOwner.slice(-4)}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Chain Selection */}
        <div className="space-y-3">
          <Label htmlFor="chain" className="text-sm font-medium text-[#4A148C]">
            Blockchain Network
          </Label>
          <Select
            id="chain"
            value={formData.chain}
            onChange={(e) => handleInputChange('chain', e.target.value as SupportedChain)}
          >
            {SUPPORTED_CHAINS.map(chain => (
              <SelectOption key={chain} value={chain}>
                {chain.charAt(0).toUpperCase() + chain.slice(1)}
                {chain === 'sepolia' && (
                  <Badge variant="secondary" className="ml-2 text-xs">Testnet</Badge>
                )}
              </SelectOption>
            ))}
          </Select>
        </div>

        {/* Contract Address */}
        <div className="space-y-3">
          <Label htmlFor="contractAddress" className="text-sm font-medium text-[#4A148C]">
            Contract Address
          </Label>
          <Input
            id="contractAddress"
            type="text"
            placeholder="0x..."
            value={formData.contractAddress}
            onChange={(e) => handleInputChange('contractAddress', e.target.value)}
          />
          <p className="text-xs text-gray-500">
            The Ethereum address that will own the ENS subname registrar contract.
          </p>
        </div>

        {/* Register Button */}
        <Button
          onClick={handleRegister}
          disabled={!isFormValid || isRegistering || !isConnected}
          className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
        >
          {isRegistering ? (
            <>
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
              Registering ENS Root...
            </>
          ) : (
            <>
              <GlobeIcon className="mr-2 h-4 w-4" />
              Register ENS Root
            </>
          )}
        </Button>

        {/* Help Text */}
        <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-blue-800 font-medium mb-1">Need an ENS domain?</p>
              <p className="text-blue-700 mb-2">
                You must own a .eth domain before registering it as a root. 
                Get one at app.ens.domains.
              </p>
              <Button
                onClick={() => window.open('https://app.ens.domains', '_blank')}
                size="sm"
                variant="outline"
                className="border-blue-300 hover:bg-blue-100 hover:border-blue-400 text-blue-700"
              >
                <ExternalLinkIcon className="mr-1 h-3 w-3" />
                Get ENS Domain
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
