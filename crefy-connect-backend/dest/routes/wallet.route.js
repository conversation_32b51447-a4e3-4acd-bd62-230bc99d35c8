"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const wallet_controller_1 = require("../controllers/wallet.controller");
const router = (0, express_1.Router)();
// Get all wallets for a user
router.get('/user/:userId', wallet_controller_1.WalletController.getUserWallets);
// Get a specific wallet by ID
router.get('/:walletId/user/:userId', wallet_controller_1.WalletController.getWallet);
exports.default = router;
