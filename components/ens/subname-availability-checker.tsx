'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { 
  SearchIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  RefreshCwIcon,
  AlertCircleIcon,
  InfoIcon
} from "lucide-react";
import { apiService } from "@/lib/api";
import { useAuth } from "@/lib/auth-context";
import { 
  SupportedChain,
  SUPPORTED_CHAINS,
  ENS_VALIDATION_RULES 
} from "@/lib/types/ens";

interface SubnameAvailabilityCheckerProps {
  ensRoot?: string;
  onAvailabilityCheck?: (subName: string, isAvailable: boolean) => void;
  className?: string;
}

interface AvailabilityResult {
  subName: string;
  isAvailable: boolean;
  chain: string;
  checkedAt: Date;
}

export function SubnameAvailabilityChecker({ 
  ensRoot = '', 
  onAvailabilityCheck, 
  className = "" 
}: SubnameAvailabilityCheckerProps) {
  const { showToast } = useToast();
  const { token } = useAuth();

  const [subName, setSubName] = useState('');
  const [chain, setChain] = useState<SupportedChain>('sepolia');
  const [isChecking, setIsChecking] = useState(false);
  const [availabilityResult, setAvailabilityResult] = useState<AvailabilityResult | null>(null);
  const [recentChecks, setRecentChecks] = useState<AvailabilityResult[]>([]);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Validate subname format
  const validateSubname = useCallback((name: string): string | null => {
    if (!name) return null;
    
    if (name.length < ENS_VALIDATION_RULES.MIN_LENGTH) {
      return `Subname must be at least ${ENS_VALIDATION_RULES.MIN_LENGTH} characters long`;
    }
    
    if (name.length > ENS_VALIDATION_RULES.MAX_LENGTH) {
      return `Subname must be no more than ${ENS_VALIDATION_RULES.MAX_LENGTH} characters long`;
    }
    
    if (!ENS_VALIDATION_RULES.ALLOWED_CHARACTERS.test(name)) {
      return 'Subname can only contain lowercase letters, numbers, and hyphens';
    }
    
    if (name.startsWith('-') || name.endsWith('-')) {
      return 'Subname cannot start or end with a hyphen';
    }
    
    if (ENS_VALIDATION_RULES.RESERVED_NAMES.includes(name.toLowerCase())) {
      return 'This subname is reserved and cannot be used';
    }
    
    return null;
  }, []);

  // Validate subname on input change
  useEffect(() => {
    const error = validateSubname(subName);
    setValidationError(error);
    
    // Clear previous result if subname changed
    if (availabilityResult && availabilityResult.subName !== subName) {
      setAvailabilityResult(null);
    }
  }, [subName, validateSubname, availabilityResult]);

  const handleSubnameChange = (value: string) => {
    // Convert to lowercase and remove invalid characters
    const cleanValue = value.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setSubName(cleanValue);
  };

  const checkAvailability = async () => {
    if (!token) {
      showToast({
        type: 'error',
        title: 'Authentication Required',
        description: 'Please log in to check subname availability'
      });
      return;
    }

    if (!subName) {
      showToast({
        type: 'error',
        title: 'Subname Required',
        description: 'Please enter a subname to check'
      });
      return;
    }

    if (validationError) {
      showToast({
        type: 'error',
        title: 'Invalid Subname',
        description: validationError
      });
      return;
    }

    setIsChecking(true);

    try {
      const response = await apiService.checkSubnameAvailability(subName, chain, token);

      if (response.success && response.data) {
        const result: AvailabilityResult = {
          subName,
          isAvailable: response.data.available,
          chain,
          checkedAt: new Date()
        };

        setAvailabilityResult(result);
        
        // Add to recent checks (keep last 5)
        setRecentChecks(prev => {
          const filtered = prev.filter(check => 
            !(check.subName === subName && check.chain === chain)
          );
          return [result, ...filtered].slice(0, 5);
        });

        // Call callback
        onAvailabilityCheck?.(subName, response.data.available);

        showToast({
          type: response.data.available ? 'success' : 'info',
          title: response.data.available ? 'Available' : 'Not Available',
          description: response.data.available 
            ? `${subName} is available for claiming`
            : `${subName} is already taken`
        });
      } else {
        throw new Error(response.error || 'Failed to check availability');
      }
    } catch (error: any) {
      console.error('Availability check error:', error);
      showToast({
        type: 'error',
        title: 'Check Failed',
        description: error.message || 'Failed to check subname availability'
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isChecking && !validationError && subName) {
      checkAvailability();
    }
  };

  const getFullSubname = () => {
    if (!subName) return '';
    return ensRoot ? `${subName}.${ensRoot}` : `${subName}.[your-root].eth`;
  };

  return (
    <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
      <CardHeader>
        <CardTitle className="text-xl font-semibold flex items-center gap-2 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
          <SearchIcon className="h-5 w-5 text-[#4A148C]" />
          Check Subname Availability
        </CardTitle>
        <p className="text-sm text-gray-600">
          Check if a subname is available for claiming under your ENS root domain.
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Subname Input */}
        <div className="space-y-3">
          <Label htmlFor="subname" className="text-sm font-medium text-[#4A148C]">
            Subname
          </Label>
          <div className="flex gap-2">
            <Input
              id="subname"
              type="text"
              placeholder="username"
              value={subName}
              onChange={(e) => handleSubnameChange(e.target.value)}
              onKeyPress={handleKeyPress}
              className={`flex-1 ${validationError ? 'border-red-300 focus:border-red-500' : ''}`}
            />
            <Button
              onClick={checkAvailability}
              disabled={!subName || isChecking || !!validationError}
              className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25"
            >
              {isChecking ? (
                <>
                  <RefreshCwIcon className="mr-1 h-3 w-3 animate-spin" />
                  Checking
                </>
              ) : (
                <>
                  <SearchIcon className="mr-1 h-3 w-3" />
                  Check
                </>
              )}
            </Button>
          </div>
          
          {/* Preview */}
          <div className="text-sm text-gray-600">
            Preview: <span className="font-mono bg-gray-100 px-2 py-1 rounded">
              {getFullSubname()}
            </span>
          </div>

          {/* Validation Error */}
          {validationError && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircleIcon className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-800">{validationError}</span>
              </div>
            </div>
          )}
        </div>

        {/* Chain Selection */}
        <div className="space-y-3">
          <Label htmlFor="chain" className="text-sm font-medium text-[#4A148C]">
            Blockchain Network
          </Label>
          <Select
            id="chain"
            value={chain}
            onChange={(e) => setChain(e.target.value as SupportedChain)}
          >
            {SUPPORTED_CHAINS.map(chainOption => (
              <SelectOption key={chainOption} value={chainOption}>
                {chainOption.charAt(0).toUpperCase() + chainOption.slice(1)}
                {chainOption === 'sepolia' && (
                  <Badge variant="secondary" className="ml-2 text-xs">Testnet</Badge>
                )}
              </SelectOption>
            ))}
          </Select>
        </div>

        {/* Availability Result */}
        {availabilityResult && (
          <div className={`p-4 rounded-lg border ${
            availabilityResult.isAvailable 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              {availabilityResult.isAvailable ? (
                <CheckCircleIcon className="h-5 w-5 text-green-600" />
              ) : (
                <XCircleIcon className="h-5 w-5 text-red-600" />
              )}
              <span className={`font-semibold ${
                availabilityResult.isAvailable ? 'text-green-800' : 'text-red-800'
              }`}>
                {availabilityResult.isAvailable ? 'Available' : 'Not Available'}
              </span>
            </div>
            <p className={`text-sm ${
              availabilityResult.isAvailable ? 'text-green-700' : 'text-red-700'
            }`}>
              {availabilityResult.isAvailable 
                ? `${availabilityResult.subName} is available for claiming`
                : `${availabilityResult.subName} is already taken`
              }
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Checked on {availabilityResult.chain} at {availabilityResult.checkedAt.toLocaleTimeString()}
            </p>
          </div>
        )}

        {/* Recent Checks */}
        {recentChecks.length > 0 && (
          <div className="space-y-3">
            <Label className="text-sm font-medium text-[#4A148C]">Recent Checks</Label>
            <div className="space-y-2">
              {recentChecks.map((check, index) => (
                <div key={`${check.subName}-${check.chain}-${index}`} 
                     className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    {check.isAvailable ? (
                      <CheckCircleIcon className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircleIcon className="h-4 w-4 text-red-600" />
                    )}
                    <span className="font-mono text-sm">{check.subName}</span>
                    <Badge variant="secondary" className="text-xs">
                      {check.chain}
                    </Badge>
                  </div>
                  <span className={`text-xs font-medium ${
                    check.isAvailable ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {check.isAvailable ? 'Available' : 'Taken'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <InfoIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-blue-800 font-medium mb-1">Subname Rules</p>
              <ul className="text-blue-700 text-xs space-y-1">
                <li>• 3-63 characters long</li>
                <li>• Lowercase letters, numbers, and hyphens only</li>
                <li>• Cannot start or end with a hyphen</li>
                <li>• Some names are reserved</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
