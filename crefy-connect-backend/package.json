{"name": "crefy-connect-backend", "version": "1.0.0", "main": "dest/app.js", "devDependencies": {"@sendgrid/mail": "^8.1.5", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-session": "^1.18.2", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.10", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "cors": "^2.8.5", "dotenv": "^16.5.0", "ethers": "^6.14.3", "express": "^5.1.0", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "scripts": {"build": "tsc ", "start": "node dest/server.js", "dev": "concurrently \"nodemon\" \"tsc -w\"", "serve": "nodemon src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@types/socket.io": "^3.0.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "concurrently": "^9.1.2", "connect-mongo": "^5.1.0", "express-session": "^1.18.1", "google-auth-library": "^9.15.1", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "viem": "^2.30.6"}}