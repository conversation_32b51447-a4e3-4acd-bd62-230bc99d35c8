"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAuth = exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const dotenv_1 = __importDefault(require("dotenv"));
const mongoose_1 = __importDefault(require("mongoose"));
dotenv_1.default.config();
const authenticate = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
        return res.status(401).json({ error: 'Authorization header required' });
    }
    const token = authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Bearer token required' });
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        // Validate the userId is a proper ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(decoded.userId)) {
            return res.status(400).json({ error: 'Invalid user ID format' });
        }
        req.user = { userId: decoded.userId };
        next();
    }
    catch (error) {
        const message = error instanceof Error ? error.message : 'Invalid token';
        res.status(403).json({ error: message });
    }
};
exports.authenticate = authenticate;
const requireAuth = async (req, res, next) => {
    try {
        // const { crefyId } = req.body;
        // if (!crefyId) {
        //     return res.status(400).json({ message: 'crefyId is required in request body' });
        // }
        // const user = await User.findOne({ crefyId });
        // if (!user) {
        //     return res.status(404).json({ message: 'CrefyId is not valid' });
        // }
        // // Attach user and crefyId to request object
        // req.user = user;
        // // @ts-ignore
        // req.crefyId = crefyId;
        next();
    }
    catch (error) {
        console.error('Auth middleware error:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};
exports.requireAuth = requireAuth;
