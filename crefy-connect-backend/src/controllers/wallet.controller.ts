import { Request, Response } from 'express';
import { WalletService } from '../services/wallet.service';
import { CreateWalletRequest } from '../types/walletTypes';

export class WalletController {
    static async getUserWallets(req: Request, res: Response) {
        try {
            const { crefyId } = req.params;

            if (!crefyId) {
                return res.status(400).json({ error: 'User ID is required' });
            }

            const wallets = await WalletService.getWalletsByUser(crefyId);
            res.status(200).json(wallets);
        } catch (error) {
            console.error('Error getting wallets:', error);
            res.status(500).json({ error: error instanceof Error ? error.message : 'Failed to get wallets' });
        }
    }

    static async getWallet(req: Request, res: Response) {
        try {
            const { crefyId, walletId } = req.params;

            if (!crefyId || !walletId) {
                return res.status(400).json({ error: 'User ID and Wallet ID are required' });
            }

            const wallet = await WalletService.getWalletById(crefyId, walletId);
            if (!wallet) {
                return res.status(404).json({ error: 'Wallet not found' });
            }

            res.status(200).json(wallet);
        } catch (error) {
            console.error('Error getting wallet:', error);
            res.status(500).json({ error: error instanceof Error ? error.message : 'Failed to get wallet' });
        }
    }
}