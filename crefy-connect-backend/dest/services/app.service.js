"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const App_1 = __importDefault(require("../models/App"));
const generators_1 = require("../utils/generators");
const mongoose_1 = __importDefault(require("mongoose"));
class AppService {
    static async createApp(data) {
        // Convert developerId to ObjectId
        const developerId = new mongoose_1.default.Types.ObjectId(data.developerId);
        const app = await App_1.default.create({
            name: data.name,
            developer: developerId, // Use the ObjectId
            allowedDomains: data.allowedDomains || [],
            appId: (0, generators_1.generateAppId)(),
            apiKey: (0, generators_1.generateApiKey)()
        });
        return app;
    }
    static async getAppsByDeveloper(developerId) {
        const id = new mongoose_1.default.Types.ObjectId(developerId);
        return await App_1.default.find({ developer: id })
            .select('-apiKey -__v')
            .lean();
    }
    static async verifyAppCredentials(appId, apiKey) {
        return await App_1.default.findOne({ appId, apiKey })
            .select('-__v')
            .lean();
    }
}
exports.AppService = AppService;
