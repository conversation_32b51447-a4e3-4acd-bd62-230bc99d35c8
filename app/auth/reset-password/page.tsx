"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { apiService } from "@/lib/api";

export default function ResetPasswordPage() {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [token, setToken] = useState("");
  const [status, setStatus] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [isTokenChecking, setIsTokenChecking] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const tokenParam = searchParams?.get("token");
    
    if (!tokenParam) {
      setStatus("Missing reset token. Please use the link from your email.");
      setIsTokenChecking(false);
      return;
    }

    setToken(tokenParam);
    
    // Verify the token
    const verifyToken = async () => {
      try {
        const response = await apiService.verifyResetToken(tokenParam);
        
        if (response.success) {
          setIsTokenValid(true);
        } else {
          setStatus(response.error || "Invalid or expired reset token. Please request a new one.");
          setIsTokenValid(false);
        }
      } catch (error) {
        console.error('Token verification error:', error);
        setStatus("Error verifying reset token. Please try again.");
        setIsTokenValid(false);
      } finally {
        setIsTokenChecking(false);
      }
    };

    verifyToken();
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Basic client-side validation
    if (!newPassword.trim()) {
      setStatus("Please enter a new password.");
      return;
    }

    if (newPassword.length < 6) {
      setStatus("Password must be at least 6 characters long.");
      return;
    }

    if (newPassword !== confirmPassword) {
      setStatus("Passwords do not match.");
      return;
    }

    setIsLoading(true);
    setStatus("Resetting password...");

    try {
      const response = await apiService.resetPassword({
        token,
        newPassword,
      });

      if (response.success) {
        setStatus("Password reset successful!");
        setIsSuccess(true);
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push("/auth/signin");
        }, 3000);
      } else {
        setStatus(response.error || "Failed to reset password");
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setStatus("Error resetting password. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isTokenChecking) {
    return (
      <main className="min-h-screen flex items-center justify-center bg-white relative overflow-hidden px-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#7B1FA2] mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying reset token...</p>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen flex items-center justify-center bg-white relative overflow-hidden px-4">
      {/* Animated Background Elements */}
      <div className="absolute w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float" style={{ top: "20%", left: "10%" }}></div>
      <div className="absolute w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow" style={{ bottom: "20%", right: "10%" }}></div>

      {/* Reset Password Card */}
      <div className="relative z-10 w-full max-w-md p-8 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl space-y-6 transition-all duration-300 hover:shadow-2xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            Reset Your Password
          </h1>
          <p className="text-gray-600 mt-2">
            {isSuccess 
              ? "Your password has been reset successfully" 
              : isTokenValid 
                ? "Create a new password for your account" 
                : "Invalid or expired reset token"
            }
          </p>
        </div>

        {isTokenValid && !isSuccess ? (
          <form onSubmit={handleSubmit} className="space-y-5">
            <div className="space-y-2">
              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700">
                New Password
              </label>
              <input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
                disabled={isLoading}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="••••••••"
                minLength={6}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm Password
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                disabled={isLoading}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="••••••••"
                minLength={6}
              />
            </div>

            {/* Status Message */}
            {status && (
              <p
                className={`text-sm ${
                  status.includes("Error") ||
                  status.includes("Failed") ||
                  status.includes("do not match") ||
                  status.includes("Please enter") ||
                  status.includes("must be")
                    ? "text-red-500"
                    : status.includes("successful")
                    ? "text-green-600"
                    : "text-gray-600"
                }`}
              >
                {status}
              </p>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 bg-[#4A148C] text-white rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg font-medium disabled:opacity-50"
            >
              {isLoading ? "Resetting..." : "Reset Password"}
            </button>
          </form>
        ) : isSuccess ? (
          <div className="space-y-5">
            <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
              <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-green-800 mb-2">Password Reset Successful!</h3>
              <p className="text-sm text-green-700">
                Your password has been reset successfully. You will be redirected to the login page in a few seconds.
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-5">
            <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200">
              <div className="w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-red-800 mb-2">Invalid Reset Link</h3>
              <p className="text-sm text-red-700">
                {status || "The password reset link is invalid or has expired. Please request a new one."}
              </p>
            </div>

            <Link
              href="/auth/forgot-password"
              className="block w-full py-3 text-center bg-[#4A148C] text-white rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg font-medium"
            >
              Request New Reset Link
            </Link>
          </div>
        )}

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Remember your password?{" "}
            <Link href="/auth/signin" className="text-[#4A148C] hover:underline font-medium">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}
